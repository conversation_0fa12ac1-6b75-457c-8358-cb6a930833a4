// AI-Enabled Recruitment System Schema
generator client {
  provider = "prisma-client-js"
  binaryTargets = ["native", "linux-musl-arm64-openssl-3.0.x"]
  output = "/home/<USER>/ai_recruitment_system/app/node_modules/.prisma/client"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// NextAuth required models
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

// Core User Management
model User {
  id               String           @id @default(cuid())
  email            String           @unique
  password         String?
  name             String?
  image            String?
  emailVerified    DateTime?
  userType         UserType         @default(JOB_SEEKER)
  subscriptionTier SubscriptionTier @default(BASIC)
  isActive         Boolean          @default(true)
  createdAt        DateTime         @default(now())
  updatedAt        DateTime         @updatedAt

  // NextAuth relations
  accounts Account[]
  sessions Session[]

  // Profile relations
  profile Profile?

  // Job seeker relations
  applications     Application[]
  jobMatches       JobMatch[]
  resumeVersions   ResumeVersion[]
  savedJobs        SavedJob[]

  // Recruiter relations
  postedJobs Job[] @relation("JobPoster")
  company    Company? @relation(fields: [companyId], references: [id])
  companyId  String?

  // Communication
  sentMessages     Message[] @relation("MessageSender")
  receivedMessages Message[] @relation("MessageReceiver")
  notifications    Notification[]

  // Analytics
  analyticsEvents      AnalyticsEvent[]
  interviewSessions    InterviewSession[] @relation("Interviewee")
  conductedInterviews  InterviewSession[] @relation("Interviewer")

  // Enhanced features
  candidateRankings CandidateRanking[]
  timeSlots         InterviewTimeSlot[] @relation("RecruiterTimeSlots")
  statusUpdates     StatusUpdate[]

  // Legal compliance relations
  auditLogs      AuditLog[]
  cookieConsents CookieConsent[]

  @@map("users")
}

model Profile {
  id                   String             @id @default(cuid())
  userId               String             @unique
  firstName            String?
  lastName             String?
  phone                String?
  location             String?
  linkedinUrl          String?
  portfolioUrl         String?
  bio                  String?            @db.Text
  skills               String[]
  experienceLevel      ExperienceLevel?
  targetSalaryMin      Int?
  targetSalaryMax      Int?
  targetRoles          String[]
  targetLocations      String[]
  remotePreference     RemotePreference   @default(HYBRID)
  availabilityDate     DateTime?
  resumeUrl            String?
  resumeData           Json?              // Parsed resume content
  profileCompleteness  Int                @default(0)
  createdAt            DateTime           @default(now())
  updatedAt            DateTime           @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("profiles")
}

// Company Management
model Company {
  id          String      @id @default(cuid())
  name        String
  slug        String      @unique
  description String?     @db.Text
  industry    String?
  size        CompanySize?
  location    String?
  website     String?
  logoUrl     String?
  isVerified  Boolean     @default(false)
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  jobs  Job[]
  users User[]

  @@map("companies")
}

// Job Management
model Job {
  id                  String            @id @default(cuid())
  title               String
  slug                String            @unique
  description         String            @db.Text
  requirements        Json?             // Structured requirements
  responsibilities    String?           @db.Text
  benefits            String?           @db.Text
  salaryMin           Int?
  salaryMax           Int?
  currency            String            @default("USD")
  location            String?
  isRemote            Boolean           @default(false)
  remoteOption        RemotePreference  @default(HYBRID)
  jobType             JobType           @default(FULL_TIME)
  experienceLevel     ExperienceLevel?
  skillsRequired      String[]
  status              JobStatus         @default(ACTIVE)
  applicationDeadline DateTime?
  startDate           DateTime?
  postedDate          DateTime          @default(now())
  updatedAt           DateTime          @updatedAt
  viewCount           Int               @default(0)
  applicationCount    Int               @default(0)

  // Relations
  companyId String
  company   Company @relation(fields: [companyId], references: [id])
  posterId  String
  poster    User    @relation("JobPoster", fields: [posterId], references: [id])

  applications       Application[]
  jobMatches         JobMatch[]
  savedJobs          SavedJob[]
  resumeVersions     ResumeVersion[]
  candidateRankings  CandidateRanking[]
  shortlistConfig    ShortlistConfig?

  @@map("jobs")
}

// Application Management
model Application {
  id               String            @id @default(cuid())
  jobId            String
  userId           String
  resumeVersionId  String?
  coverLetter      String?           @db.Text
  status           ApplicationStatus @default(SUBMITTED)
  aiMatchScore     Float?
  customResumeData Json?
  appliedDate      DateTime          @default(now())
  updatedAt        DateTime          @updatedAt
  recruiterNotes   String?           @db.Text
  rejectionReason  String?

  // Relations
  job             Job               @relation(fields: [jobId], references: [id])
  user            User              @relation(fields: [userId], references: [id])
  resumeVersion   ResumeVersion?    @relation(fields: [resumeVersionId], references: [id])
  interviewSessions InterviewSession[]
  candidateRanking CandidateRanking?

  @@unique([jobId, userId])
  @@map("applications")
}

model InterviewSession {
  id              String          @id @default(cuid())
  applicationId   String
  interviewerId   String?
  intervieweeId   String
  timeSlotId      String?
  type            InterviewType   @default(VIDEO)
  scheduledDate   DateTime
  duration        Int?            // in minutes
  status          InterviewStatus @default(SCHEDULED)
  meetingUrl      String?
  meetingRoomId   String?
  notes           String?         @db.Text
  feedback        Json?
  rating          Int?            // 1-5 scale
  reminderSent    Boolean         @default(false)
  calendarEventId String?         // For calendar integration
  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @updatedAt

  // Relations
  application  Application @relation(fields: [applicationId], references: [id])
  interviewer  User?       @relation("Interviewer", fields: [interviewerId], references: [id])
  interviewee  User        @relation("Interviewee", fields: [intervieweeId], references: [id])
  timeSlot     InterviewTimeSlot? @relation(fields: [timeSlotId], references: [id])

  @@map("interview_sessions")
}

// AI & Matching
model JobMatch {
  id           String   @id @default(cuid())
  userId       String
  jobId        String
  matchScore   Float
  matchReasons Json?    // Array of reasons for the match
  isViewed     Boolean  @default(false)
  isApplied    Boolean  @default(false)
  createdAt    DateTime @default(now())

  // Relations
  user User @relation(fields: [userId], references: [id])
  job  Job  @relation(fields: [jobId], references: [id])

  @@unique([userId, jobId])
  @@map("job_matches")
}

// Enhanced AI Ranking System
model CandidateRanking {
  id                     String   @id @default(cuid())
  jobId                  String
  userId                 String
  applicationId          String?  @unique
  overallScore           Float    // 0-100 overall match score
  skillsMatchScore       Float    // Skills compatibility score
  experienceScore        Float    // Experience level score
  roleCompatibilityScore Float    // Role fit score
  seniorityScore         Float    // Seniority level match
  technicalSkillsScore   Float    // Technical skills score
  softSkillsScore        Float    // Soft skills score
  skillGaps              Json?    // Missing skills analysis
  recommendations        Json?    // Improvement recommendations
  ranking                Int?     // Rank among all candidates for this job
  isShortlisted          Boolean  @default(false)
  shortlistedAt          DateTime?
  reasonsForRanking      Json?    // Detailed explanations
  createdAt              DateTime @default(now())
  updatedAt              DateTime @updatedAt

  // Relations
  job         Job          @relation(fields: [jobId], references: [id])
  user        User         @relation(fields: [userId], references: [id])
  application Application? @relation(fields: [applicationId], references: [id])

  @@unique([jobId, userId])
  @@map("candidate_rankings")
}

// Interview Scheduling System
model InterviewTimeSlot {
  id             String   @id @default(cuid())
  recruiterId    String
  date           DateTime
  startTime      DateTime
  endTime        DateTime
  isAvailable    Boolean  @default(true)
  isRecurring    Boolean  @default(false)
  recurringDays  String[] // Days of week for recurring slots
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relations
  recruiter  User               @relation("RecruiterTimeSlots", fields: [recruiterId], references: [id])
  interviews InterviewSession[]

  @@map("interview_time_slots")
}

// Real-time Status Tracking
model StatusUpdate {
  id             String     @id @default(cuid())
  entityType     EntityType // APPLICATION, INTERVIEW, JOB
  entityId       String     // ID of the application, interview, etc.
  previousStatus String?
  newStatus      String
  userId         String?    // User who made the change
  description    String?
  metadata       Json?      // Additional context
  timestamp      DateTime   @default(now())

  // Relations
  user User? @relation(fields: [userId], references: [id])

  @@map("status_updates")
}

// Auto-shortlisting Configuration
model ShortlistConfig {
  id               String    @id @default(cuid())
  jobId            String    @unique
  maxCandidates    Int       @default(5)
  minScore         Float     @default(70.0)
  autoShortlist    Boolean   @default(true)
  lastProcessedAt  DateTime?
  triggerThreshold Int       @default(10) // Number of applications to trigger auto-shortlisting
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt

  // Relations
  job Job @relation(fields: [jobId], references: [id])

  @@map("shortlist_configs")
}

model ResumeVersion {
  id                   String   @id @default(cuid())
  userId               String
  jobId                String?
  title                String   @default("Default Resume")
  content              Json     // Structured resume content
  optimizationScore    Float?
  atsCompatibilityScore Float?
  keywords             String[]
  isDefault            Boolean  @default(false)
  fileUrl              String?
  createdAt            DateTime @default(now())
  updatedAt            DateTime @updatedAt

  // Relations
  user         User          @relation(fields: [userId], references: [id])
  job          Job?          @relation(fields: [jobId], references: [id])
  applications Application[]

  @@map("resume_versions")
}

model SavedJob {
  id        String   @id @default(cuid())
  userId    String
  jobId     String
  createdAt DateTime @default(now())

  // Relations
  user User @relation(fields: [userId], references: [id])
  job  Job  @relation(fields: [jobId], references: [id])

  @@unique([userId, jobId])
  @@map("saved_jobs")
}

// Communication
model Message {
  id          String      @id @default(cuid())
  senderId    String
  receiverId  String
  subject     String?
  content     String      @db.Text
  isRead      Boolean     @default(false)
  messageType MessageType @default(DIRECT)
  threadId    String?     // For conversation threading
  attachments Json?       // File attachments
  isRealTime  Boolean     @default(false)
  deliveredAt DateTime?
  readAt      DateTime?
  createdAt   DateTime    @default(now())

  // Relations
  sender   User @relation("MessageSender", fields: [senderId], references: [id])
  receiver User @relation("MessageReceiver", fields: [receiverId], references: [id])

  @@map("messages")
}

model Notification {
  id        String           @id @default(cuid())
  userId    String
  title     String
  content   String           @db.Text
  type      NotificationType
  isRead    Boolean          @default(false)
  actionUrl String?
  createdAt DateTime         @default(now())

  // Relations
  user User @relation(fields: [userId], references: [id])

  @@map("notifications")
}

// Analytics
model AnalyticsEvent {
  id        String   @id @default(cuid())
  userId    String?
  eventType String
  eventData Json?
  sessionId String?
  ipAddress String?
  userAgent String?
  timestamp DateTime @default(now())

  // Relations
  user User? @relation(fields: [userId], references: [id])

  @@map("analytics_events")
}

// Legal Compliance Models
model AuditLog {
  id          String          @id @default(cuid())
  userId      String?
  actionType  AuditActionType
  entityType  String          // "job_source", "scraped_job", etc.
  entityId    String?
  oldValues   Json?
  newValues   Json?
  details     String?         @db.Text
  ipAddress   String?
  userAgent   String?
  metadata    Json?
  createdAt   DateTime        @default(now())

  // Relations
  user User? @relation(fields: [userId], references: [id])

  @@index([actionType, createdAt])
  @@index([entityType, entityId])
  @@map("audit_logs")
}

// Cookie consent and privacy tracking
model CookieConsent {
  id           String        @id @default(cuid())
  userId       String?
  sessionId    String?
  consentGiven Boolean       @default(false)
  consentType  ConsentType[] // NECESSARY, FUNCTIONAL, ANALYTICS, MARKETING
  ipAddress    String?
  userAgent    String?
  consentDate  DateTime      @default(now())
  withdrawnDate DateTime?
  isWithdrawn  Boolean       @default(false)

  // Relations
  user User? @relation(fields: [userId], references: [id])

  @@map("cookie_consents")
}

// Enums
enum UserType {
  JOB_SEEKER
  RECRUITER
  ADMIN
  CAREER_COACH
}

enum SubscriptionTier {
  BASIC
  PREMIUM
  ENTERPRISE
}

enum ExperienceLevel {
  ENTRY
  JUNIOR
  MID
  SENIOR
  EXECUTIVE
}

enum RemotePreference {
  REMOTE
  ONSITE
  HYBRID
}

enum CompanySize {
  STARTUP
  SMALL
  MEDIUM
  LARGE
  ENTERPRISE
}

enum JobType {
  FULL_TIME
  PART_TIME
  CONTRACT
  FREELANCE
  INTERNSHIP
}

enum JobStatus {
  DRAFT
  ACTIVE
  PAUSED
  CLOSED
  EXPIRED
}

enum ApplicationStatus {
  SUBMITTED
  UNDER_REVIEW
  SHORTLISTED
  INTERVIEW_SCHEDULED
  INTERVIEWED
  OFFER_EXTENDED
  HIRED
  REJECTED
  WITHDRAWN
}

enum InterviewType {
  PHONE
  VIDEO
  IN_PERSON
  TECHNICAL
  BEHAVIORAL
}

enum InterviewStatus {
  SCHEDULED
  IN_PROGRESS
  COMPLETED
  CANCELLED
  NO_SHOW
}

enum MessageType {
  DIRECT
  APPLICATION_UPDATE
  INTERVIEW_INVITE
  SYSTEM
}

enum NotificationType {
  APPLICATION_UPDATE
  INTERVIEW_SCHEDULED
  JOB_MATCH
  MESSAGE_RECEIVED
  RESUME_FEEDBACK
  SYSTEM_ALERT
}

enum EntityType {
  APPLICATION
  INTERVIEW
  JOB
  USER
}

enum AuditActionType {
  CREATE
  UPDATE
  DELETE
  VIEW
  EXPORT
  SCRAPE
  API_CALL
  LOGIN
  LOGOUT
  CONSENT_GIVEN
  CONSENT_WITHDRAWN
  DATA_EXPORT
  DATA_DELETION
}

enum ConsentType {
  NECESSARY
  FUNCTIONAL
  ANALYTICS
  MARKETING
}
