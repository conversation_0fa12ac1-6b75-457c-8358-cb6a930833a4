import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/database';
import { rankCandidates } from '@/lib/ai/ranking';
import { authenticateUser } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    const user = await authenticateUser(request);
    if (!user || user.userType !== 'RECRUITER') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { jobId } = await request.json();

    // Get job and applications
    const job = await prisma.job.findUnique({
      where: { id: jobId },
      include: {
        applications: {
          include: {
            user: {
              include: { profile: true }
            },
            resumeVersion: true
          }
        }
      }
    });

    if (!job) {
      return NextResponse.json({ error: 'Job not found' }, { status: 404 });
    }

    // Rank candidates using AI
    const rankings = await rankCandidates(job, job.applications);

    // Save rankings to database
    const savedRankings = await Promise.all(
      rankings.map(async (ranking) => {
        return prisma.candidateRanking.upsert({
          where: {
            jobId_userId: {
              jobId: jobId,
              userId: ranking.userId
            }
          },
          update: {
            overallScore: ranking.overallScore,
            skillsMatchScore: ranking.skillsMatchScore,
            experienceScore: ranking.experienceScore,
            roleCompatibilityScore: ranking.roleCompatibilityScore,
            seniorityScore: ranking.seniorityScore,
            technicalSkillsScore: ranking.technicalSkillsScore,
            softSkillsScore: ranking.softSkillsScore,
            skillGaps: ranking.skillGaps,
            recommendations: ranking.recommendations,
            ranking: ranking.ranking,
            reasonsForRanking: ranking.reasonsForRanking
          },
          create: {
            jobId: jobId,
            userId: ranking.userId,
            applicationId: ranking.applicationId,
            overallScore: ranking.overallScore,
            skillsMatchScore: ranking.skillsMatchScore,
            experienceScore: ranking.experienceScore,
            roleCompatibilityScore: ranking.roleCompatibilityScore,
            seniorityScore: ranking.seniorityScore,
            technicalSkillsScore: ranking.technicalSkillsScore,
            softSkillsScore: ranking.softSkillsScore,
            skillGaps: ranking.skillGaps,
            recommendations: ranking.recommendations,
            ranking: ranking.ranking,
            reasonsForRanking: ranking.reasonsForRanking
          }
        });
      })
    );

    return NextResponse.json({ rankings: savedRankings });
  } catch (error) {
    console.error('Candidate ranking error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const user = await authenticateUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const jobId = searchParams.get('jobId');

    if (!jobId) {
      return NextResponse.json({ error: 'Job ID required' }, { status: 400 });
    }

    // Get candidate rankings for job
    const rankings = await prisma.candidateRanking.findMany({
      where: { jobId },
      include: {
        user: {
          include: { profile: true }
        },
        application: true
      },
      orderBy: { ranking: 'asc' }
    });

    return NextResponse.json({ rankings });
  } catch (error) {
    console.error('Get candidate rankings error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
