import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/database';
import { calculateJobMatch } from '@/lib/ai/matching';
import { authenticateUser } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    const user = await authenticateUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { jobId } = await request.json();

    // Get user profile and job details
    const [userProfile, job] = await Promise.all([
      prisma.profile.findUnique({
        where: { userId: user.id }
      }),
      prisma.job.findUnique({
        where: { id: jobId },
        include: { company: true }
      })
    ]);

    if (!userProfile || !job) {
      return NextResponse.json(
        { error: 'User profile or job not found' },
        { status: 404 }
      );
    }

    // Calculate match score using AI
    const matchResult = await calculateJobMatch(userProfile, job);

    // Save or update job match
    const jobMatch = await prisma.jobMatch.upsert({
      where: {
        userId_jobId: {
          userId: user.id,
          jobId: jobId
        }
      },
      update: {
        matchScore: matchResult.score,
        matchReasons: matchResult.reasons
      },
      create: {
        userId: user.id,
        jobId: jobId,
        matchScore: matchResult.score,
        matchReasons: matchResult.reasons
      }
    });

    return NextResponse.json({
      matchScore: matchResult.score,
      matchReasons: matchResult.reasons,
      recommendations: matchResult.recommendations
    });
  } catch (error) {
    console.error('Job matching error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const user = await authenticateUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '10');
    const minScore = parseFloat(searchParams.get('minScore') || '70');

    // Get top job matches for user
    const jobMatches = await prisma.jobMatch.findMany({
      where: {
        userId: user.id,
        matchScore: { gte: minScore }
      },
      include: {
        job: {
          include: { company: true }
        }
      },
      orderBy: { matchScore: 'desc' },
      take: limit
    });

    return NextResponse.json({ matches: jobMatches });
  } catch (error) {
    console.error('Get job matches error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
