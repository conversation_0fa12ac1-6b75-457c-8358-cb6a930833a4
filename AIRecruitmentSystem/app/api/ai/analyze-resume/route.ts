import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/database';
import { analyzeResume } from '@/lib/ai/resume-analysis';
import { authenticateUser } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    const user = await authenticateUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const formData = await request.formData();
    const file = formData.get('resume') as File;
    const jobId = formData.get('jobId') as string;

    if (!file) {
      return NextResponse.json({ error: 'Resume file required' }, { status: 400 });
    }

    // Convert file to buffer for analysis
    const buffer = Buffer.from(await file.arrayBuffer());

    // Analyze resume using AI
    const analysis = await analyzeResume(buffer, file.type);

    // Get job details if jobId provided for targeted analysis
    let job = null;
    if (jobId) {
      job = await prisma.job.findUnique({
        where: { id: jobId }
      });
    }

    // Enhanced analysis with job matching if job provided
    let jobMatchAnalysis = null;
    if (job) {
      jobMatchAnalysis = await analyzeResumeForJob(analysis, job);
    }

    // Save resume version
    const resumeVersion = await prisma.resumeVersion.create({
      data: {
        userId: user.id,
        jobId: jobId || null,
        title: `Resume Analysis - ${new Date().toLocaleDateString()}`,
        content: analysis.structuredData,
        optimizationScore: analysis.optimizationScore,
        atsCompatibilityScore: analysis.atsCompatibilityScore,
        keywords: analysis.keywords
      }
    });

    return NextResponse.json({
      analysis: {
        ...analysis,
        jobMatchAnalysis
      },
      resumeVersionId: resumeVersion.id
    });
  } catch (error) {
    console.error('Resume analysis error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

async function analyzeResumeForJob(resumeAnalysis: any, job: any) {
  // Compare resume skills with job requirements
  const jobSkills = job.skillsRequired || [];
  const resumeSkills = resumeAnalysis.skills || [];

  const matchingSkills = resumeSkills.filter((skill: string) =>
    jobSkills.some((jobSkill: string) =>
      skill.toLowerCase().includes(jobSkill.toLowerCase()) ||
      jobSkill.toLowerCase().includes(skill.toLowerCase())
    )
  );

  const missingSkills = jobSkills.filter((jobSkill: string) =>
    !resumeSkills.some((skill: string) =>
      skill.toLowerCase().includes(jobSkill.toLowerCase()) ||
      jobSkill.toLowerCase().includes(skill.toLowerCase())
    )
  );

  const skillMatchPercentage = jobSkills.length > 0
    ? (matchingSkills.length / jobSkills.length) * 100
    : 0;

  return {
    skillMatchPercentage,
    matchingSkills,
    missingSkills,
    recommendations: generateRecommendations(missingSkills, resumeAnalysis)
  };
}

function generateRecommendations(missingSkills: string[], resumeAnalysis: any) {
  const recommendations = [];

  if (missingSkills.length > 0) {
    recommendations.push({
      type: 'skills',
      title: 'Add Missing Skills',
      description: `Consider adding these skills to your resume: ${missingSkills.join(', ')}`,
      priority: 'high'
    });
  }

  if (resumeAnalysis.atsCompatibilityScore < 80) {
    recommendations.push({
      type: 'ats',
      title: 'Improve ATS Compatibility',
      description: 'Use more standard formatting and include relevant keywords',
      priority: 'medium'
    });
  }

  if (resumeAnalysis.optimizationScore < 70) {
    recommendations.push({
      type: 'optimization',
      title: 'Optimize Resume Content',
      description: 'Add more quantifiable achievements and action verbs',
      priority: 'medium'
    });
  }

  return recommendations;
}
