import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/database';
import { autoShortlistCandidates } from '@/lib/ai/shortlisting';
import { authenticateUser } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    const user = await authenticateUser(request);
    if (!user || user.userType !== 'RECRUITER') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { jobId, maxCandidates, minScore } = await request.json();

    // Get or create shortlist configuration
    const shortlistConfig = await prisma.shortlistConfig.upsert({
      where: { jobId },
      update: {
        maxCandidates: maxCandidates || 5,
        minScore: minScore || 70.0,
        lastProcessedAt: new Date()
      },
      create: {
        jobId,
        maxCandidates: maxCandidates || 5,
        minScore: minScore || 70.0,
        autoShortlist: true,
        lastProcessedAt: new Date()
      }
    });

    // Get candidate rankings
    const rankings = await prisma.candidateRanking.findMany({
      where: {
        jobId,
        overallScore: { gte: shortlistConfig.minScore }
      },
      include: {
        user: { include: { profile: true } },
        application: true
      },
      orderBy: { overallScore: 'desc' },
      take: shortlistConfig.maxCandidates
    });

    // Auto-shortlist top candidates
    const shortlistedCandidates = await Promise.all(
      rankings.map(async (ranking) => {
        const updated = await prisma.candidateRanking.update({
          where: { id: ranking.id },
          data: {
            isShortlisted: true,
            shortlistedAt: new Date()
          },
          include: {
            user: { include: { profile: true } },
            application: true
          }
        });

        // Update application status
        if (ranking.applicationId) {
          await prisma.application.update({
            where: { id: ranking.applicationId },
            data: { status: 'SHORTLISTED' }
          });
        }

        return updated;
      })
    );

    return NextResponse.json({
      shortlistedCount: shortlistedCandidates.length,
      candidates: shortlistedCandidates,
      config: shortlistConfig
    });
  } catch (error) {
    console.error('Auto-shortlist error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
