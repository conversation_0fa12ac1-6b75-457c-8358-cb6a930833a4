import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Users, 
  Briefcase, 
  TrendingUp, 
  Calendar,
  Brain,
  Star,
  MessageSquare,
  BarChart3
} from 'lucide-react';

export default function DashboardPage() {
  // Mock data - in a real app, this would come from your API
  const stats = [
    {
      title: 'Active Jobs',
      value: '12',
      change: '+2 this week',
      icon: Briefcase,
      color: 'text-blue-600'
    },
    {
      title: 'Total Applications',
      value: '248',
      change: '+18 today',
      icon: Users,
      color: 'text-green-600'
    },
    {
      title: 'AI Matches',
      value: '89',
      change: '+12 new',
      icon: Brain,
      color: 'text-purple-600'
    },
    {
      title: 'Interviews Scheduled',
      value: '7',
      change: 'This week',
      icon: Calendar,
      color: 'text-orange-600'
    }
  ];

  const recentActivity = [
    {
      type: 'application',
      message: 'New application for Senior Developer position',
      time: '2 hours ago',
      badge: 'New'
    },
    {
      type: 'match',
      message: 'AI found 3 new candidate matches',
      time: '4 hours ago',
      badge: 'AI'
    },
    {
      type: 'interview',
      message: 'Interview scheduled with <PERSON>',
      time: '6 hours ago',
      badge: 'Scheduled'
    },
    {
      type: 'message',
      message: 'New message from candidate',
      time: '1 day ago',
      badge: 'Message'
    }
  ];

  const topCandidates = [
    {
      name: 'Alex Chen',
      position: 'Full Stack Developer',
      score: 95,
      status: 'Shortlisted'
    },
    {
      name: 'Sarah Johnson',
      position: 'UI/UX Designer',
      score: 92,
      status: 'Interview'
    },
    {
      name: 'Mike Rodriguez',
      position: 'DevOps Engineer',
      score: 88,
      status: 'Review'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600">Welcome back! Here's what's happening with your recruitment.</p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <Card key={index}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {stat.title}
                </CardTitle>
                <Icon className={`h-4 w-4 ${stat.color}`} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
                <p className="text-xs text-muted-foreground">
                  {stat.change}
                </p>
              </CardContent>
            </Card>
          );
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>
              Latest updates from your recruitment pipeline
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivity.map((activity, index) => (
                <div key={index} className="flex items-center space-x-4">
                  <div className="flex-1">
                    <p className="text-sm font-medium">{activity.message}</p>
                    <p className="text-xs text-gray-500">{activity.time}</p>
                  </div>
                  <Badge variant="secondary">{activity.badge}</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Top Candidates */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Star className="h-4 w-4 mr-2" />
              Top AI-Matched Candidates
            </CardTitle>
            <CardDescription>
              Highest scoring candidates from AI analysis
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {topCandidates.map((candidate, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium">{candidate.name}</p>
                    <p className="text-xs text-gray-500">{candidate.position}</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="text-right">
                      <p className="text-sm font-bold text-green-600">{candidate.score}%</p>
                      <Badge variant="outline" className="text-xs">
                        {candidate.status}
                      </Badge>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-4">
              <Button variant="outline" className="w-full">
                View All Candidates
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>
            Common tasks to help you manage your recruitment process
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button className="h-20 flex flex-col items-center justify-center space-y-2">
              <Briefcase className="h-6 w-6" />
              <span>Post New Job</span>
            </Button>
            <Button variant="outline" className="h-20 flex flex-col items-center justify-center space-y-2">
              <Brain className="h-6 w-6" />
              <span>Run AI Analysis</span>
            </Button>
            <Button variant="outline" className="h-20 flex flex-col items-center justify-center space-y-2">
              <Calendar className="h-6 w-6" />
              <span>Schedule Interview</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
