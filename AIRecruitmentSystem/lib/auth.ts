import { NextRequest } from 'next/server';
import jwt from 'jsonwebtoken';
import { prisma } from '@/lib/database';

export interface AuthenticatedUser {
  id: string;
  email: string;
  userType: string;
}

export async function authenticateUser(request: NextRequest): Promise<AuthenticatedUser | null> {
  try {
    // Try to get token from Authorization header
    const authHeader = request.headers.get('authorization');
    let token = authHeader?.replace('Bearer ', '');

    // If no token in header, try to get from cookies
    if (!token) {
      token = request.cookies.get('auth-token')?.value;
    }

    if (!token) {
      return null;
    }

    // Verify JWT token
    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;

    // Get user from database to ensure they still exist and are active
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: {
        id: true,
        email: true,
        userType: true,
        isActive: true
      }
    });

    if (!user || !user.isActive) {
      return null;
    }

    return {
      id: user.id,
      email: user.email,
      userType: user.userType
    };
  } catch (error) {
    console.error('Authentication error:', error);
    return null;
  }
}

export function generateToken(user: { id: string; email: string; userType: string }) {
  return jwt.sign(
    {
      userId: user.id,
      email: user.email,
      userType: user.userType
    },
    process.env.JWT_SECRET!,
    { expiresIn: '7d' }
  );
}

export async function requireAuth(request: NextRequest, allowedUserTypes?: string[]) {
  const user = await authenticateUser(request);
  
  if (!user) {
    throw new Error('Unauthorized');
  }

  if (allowedUserTypes && !allowedUserTypes.includes(user.userType)) {
    throw new Error('Forbidden');
  }

  return user;
}
