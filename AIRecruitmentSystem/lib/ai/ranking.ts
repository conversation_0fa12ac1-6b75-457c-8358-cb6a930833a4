// AI Candidate Ranking Logic
export async function rankCandidates(job: any, applications: any[]) {
  const rankings = [];

  for (const application of applications) {
    const candidate = application.user;
    const profile = candidate.profile;
    const resume = application.resumeVersion;

    // Calculate various scores
    const skillsMatchScore = calculateSkillsMatch(profile.skills || [], job.skillsRequired || []);
    const experienceScore = calculateExperienceScore(profile.experienceLevel, job.experienceLevel);
    const roleCompatibilityScore = calculateRoleCompatibility(profile, job);
    const seniorityScore = calculateSeniorityMatch(profile.experienceLevel, job.experienceLevel);
    const technicalSkillsScore = calculateTechnicalSkills(profile.skills || [], job.skillsRequired || []);
    const softSkillsScore = calculateSoftSkills(profile, job);

    // Calculate overall score (weighted average)
    const overallScore = (
      skillsMatchScore * 0.25 +
      experienceScore * 0.20 +
      roleCompatibilityScore * 0.15 +
      seniorityScore * 0.15 +
      technicalSkillsScore * 0.15 +
      softSkillsScore * 0.10
    );

    // Analyze skill gaps
    const skillGaps = analyzeSkillGaps(profile.skills || [], job.skillsRequired || []);

    // Generate recommendations
    const recommendations = generateCandidateRecommendations(profile, job, skillGaps);

    // Generate ranking reasons
    const reasonsForRanking = generateRankingReasons({
      skillsMatchScore,
      experienceScore,
      roleCompatibilityScore,
      profile,
      job
    });

    rankings.push({
      userId: candidate.id,
      applicationId: application.id,
      overallScore,
      skillsMatchScore,
      experienceScore,
      roleCompatibilityScore,
      seniorityScore,
      technicalSkillsScore,
      softSkillsScore,
      skillGaps,
      recommendations,
      reasonsForRanking,
      ranking: 0 // Will be set after sorting
    });
  }

  // Sort by overall score and assign rankings
  rankings.sort((a, b) => b.overallScore - a.overallScore);
  rankings.forEach((ranking, index) => {
    ranking.ranking = index + 1;
  });

  return rankings;
}

function calculateSkillsMatch(candidateSkills: string[], jobSkills: string[]): number {
  if (jobSkills.length === 0) return 100;

  const matchingSkills = candidateSkills.filter(skill =>
    jobSkills.some(jobSkill =>
      skill.toLowerCase().includes(jobSkill.toLowerCase()) ||
      jobSkill.toLowerCase().includes(skill.toLowerCase())
    )
  );

  return (matchingSkills.length / jobSkills.length) * 100;
}

function calculateExperienceScore(candidateLevel: string, jobLevel: string): number {
  const levels = ['ENTRY', 'JUNIOR', 'MID', 'SENIOR', 'EXECUTIVE'];
  const candidateIndex = levels.indexOf(candidateLevel);
  const jobIndex = levels.indexOf(jobLevel);

  if (candidateIndex === -1 || jobIndex === -1) return 50;
  if (candidateIndex === jobIndex) return 100;

  const diff = Math.abs(candidateIndex - jobIndex);
  if (diff === 1) return 80;
  if (diff === 2) return 60;
  return 40;
}

function calculateRoleCompatibility(profile: any, job: any): number {
  let score = 50; // Base score

  // Check target roles
  if (profile.targetRoles?.some((role: string) =>
    job.title.toLowerCase().includes(role.toLowerCase()) ||
    role.toLowerCase().includes(job.title.toLowerCase())
  )) {
    score += 30;
  }

  // Check location preference
  if (job.isRemote && profile.remotePreference === 'REMOTE') {
    score += 20;
  } else if (profile.targetLocations?.includes(job.location)) {
    score += 20;
  }

  return Math.min(100, score);
}

function calculateSeniorityMatch(candidateLevel: string, jobLevel: string): number {
  return calculateExperienceScore(candidateLevel, jobLevel);
}

function calculateTechnicalSkills(candidateSkills: string[], jobSkills: string[]): number {
  const technicalKeywords = ['javascript', 'python', 'java', 'react', 'node', 'sql', 'aws', 'docker', 'kubernetes'];
  
  const candidateTechSkills = candidateSkills.filter(skill =>
    technicalKeywords.some(keyword =>
      skill.toLowerCase().includes(keyword)
    )
  );

  const jobTechSkills = jobSkills.filter(skill =>
    technicalKeywords.some(keyword =>
      skill.toLowerCase().includes(keyword)
    )
  );

  if (jobTechSkills.length === 0) return 100;

  const matchingTechSkills = candidateTechSkills.filter(skill =>
    jobTechSkills.some(jobSkill =>
      skill.toLowerCase().includes(jobSkill.toLowerCase()) ||
      jobSkill.toLowerCase().includes(skill.toLowerCase())
    )
  );

  return (matchingTechSkills.length / jobTechSkills.length) * 100;
}

function calculateSoftSkills(profile: any, job: any): number {
  // This is a simplified calculation
  // In a real implementation, you would analyze the job description and profile for soft skills
  return 75; // Default score
}

function analyzeSkillGaps(candidateSkills: string[], jobSkills: string[]) {
  const missingSkills = jobSkills.filter(jobSkill =>
    !candidateSkills.some(skill =>
      skill.toLowerCase().includes(jobSkill.toLowerCase()) ||
      jobSkill.toLowerCase().includes(skill.toLowerCase())
    )
  );

  return {
    missingSkills,
    criticalGaps: missingSkills.slice(0, 3), // Top 3 most important
    totalGaps: missingSkills.length
  };
}

function generateCandidateRecommendations(profile: any, job: any, skillGaps: any) {
  const recommendations = [];

  if (skillGaps.missingSkills.length > 0) {
    recommendations.push({
      type: 'skills_development',
      title: 'Skill Development Needed',
      description: `Candidate should develop: ${skillGaps.criticalGaps.join(', ')}`,
      priority: 'high'
    });
  }

  if (profile.experienceLevel === 'ENTRY' && job.experienceLevel !== 'ENTRY') {
    recommendations.push({
      type: 'mentoring',
      title: 'Consider Mentoring Program',
      description: 'Entry-level candidate may benefit from additional support',
      priority: 'medium'
    });
  }

  return recommendations;
}

function generateRankingReasons(data: any) {
  const reasons = [];

  if (data.skillsMatchScore >= 80) {
    reasons.push('Excellent skills match for the role');
  } else if (data.skillsMatchScore >= 60) {
    reasons.push('Good skills match with some gaps');
  } else {
    reasons.push('Limited skills match - may need training');
  }

  if (data.experienceScore >= 90) {
    reasons.push('Perfect experience level match');
  } else if (data.experienceScore >= 70) {
    reasons.push('Good experience level fit');
  }

  if (data.roleCompatibilityScore >= 80) {
    reasons.push('Strong role compatibility');
  }

  return reasons;
}
