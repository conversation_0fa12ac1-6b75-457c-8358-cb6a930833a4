// AI Resume Analysis Logic
export async function analyzeResume(buffer: Buffer, fileType: string) {
  // This is a simplified resume analysis
  // In a real implementation, you would use AI services like OpenAI, or resume parsing libraries
  
  try {
    // Extract text from resume (simplified)
    const resumeText = await extractTextFromResume(buffer, fileType);
    
    // Analyze the resume content
    const analysis = {
      structuredData: await parseResumeStructure(resumeText),
      optimizationScore: calculateOptimizationScore(resumeText),
      atsCompatibilityScore: calculateATSCompatibility(resumeText),
      keywords: extractKeywords(resumeText),
      skills: extractSkills(resumeText),
      experience: extractExperience(resumeText),
      education: extractEducation(resumeText),
      suggestions: generateSuggestions(resumeText)
    };

    return analysis;
  } catch (error) {
    console.error('Resume analysis error:', error);
    throw new Error('Failed to analyze resume');
  }
}

async function extractTextFromResume(buffer: Buffer, fileType: string): Promise<string> {
  // Simplified text extraction
  // In a real implementation, you would use libraries like pdf-parse, mammoth, etc.
  
  if (fileType === 'application/pdf') {
    // For PDF files, you would use pdf-parse or similar
    return buffer.toString('utf-8'); // Simplified
  } else if (fileType.includes('word')) {
    // For Word documents, you would use mammoth or similar
    return buffer.toString('utf-8'); // Simplified
  } else {
    // For text files
    return buffer.toString('utf-8');
  }
}

async function parseResumeStructure(text: string) {
  // Simplified structure parsing
  // In a real implementation, you would use NLP to identify sections
  
  const sections = {
    personalInfo: extractPersonalInfo(text),
    summary: extractSummary(text),
    experience: extractWorkExperience(text),
    education: extractEducationDetails(text),
    skills: extractSkillsList(text),
    certifications: extractCertifications(text)
  };

  return sections;
}

function extractPersonalInfo(text: string) {
  // Extract email, phone, name, etc.
  const emailRegex = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/;
  const phoneRegex = /\b\d{3}[-.]?\d{3}[-.]?\d{4}\b/;
  
  const email = text.match(emailRegex)?.[0] || null;
  const phone = text.match(phoneRegex)?.[0] || null;
  
  return { email, phone };
}

function extractSummary(text: string) {
  // Look for summary/objective section
  const summaryRegex = /(summary|objective|profile)[\s\S]*?(?=\n\n|\n[A-Z])/i;
  const match = text.match(summaryRegex);
  return match ? match[0].trim() : null;
}

function extractWorkExperience(text: string) {
  // Simplified experience extraction
  const experienceSection = text.match(/(experience|employment|work history)[\s\S]*?(?=\n\n[A-Z]|$)/i);
  if (!experienceSection) return [];

  // Extract individual jobs (simplified)
  const jobs = experienceSection[0].split('\n').filter(line => 
    line.trim().length > 10 && 
    !line.toLowerCase().includes('experience') &&
    !line.toLowerCase().includes('employment')
  );

  return jobs.slice(0, 5).map(job => ({ title: job.trim() }));
}

function extractEducationDetails(text: string) {
  const educationSection = text.match(/(education|academic|university|college)[\s\S]*?(?=\n\n[A-Z]|$)/i);
  if (!educationSection) return [];

  const education = educationSection[0].split('\n').filter(line => 
    line.trim().length > 5 && 
    !line.toLowerCase().includes('education')
  );

  return education.slice(0, 3).map(edu => ({ degree: edu.trim() }));
}

function extractSkillsList(text: string) {
  const skillsSection = text.match(/(skills|technologies|competencies)[\s\S]*?(?=\n\n[A-Z]|$)/i);
  if (!skillsSection) return [];

  // Common technical skills
  const commonSkills = [
    'javascript', 'python', 'java', 'react', 'node', 'sql', 'html', 'css',
    'aws', 'docker', 'kubernetes', 'git', 'mongodb', 'postgresql', 'redis',
    'typescript', 'angular', 'vue', 'express', 'django', 'flask', 'spring'
  ];

  const foundSkills = commonSkills.filter(skill => 
    text.toLowerCase().includes(skill.toLowerCase())
  );

  return foundSkills;
}

function extractCertifications(text: string) {
  const certSection = text.match(/(certification|certificate|license)[\s\S]*?(?=\n\n[A-Z]|$)/i);
  if (!certSection) return [];

  return certSection[0].split('\n').filter(line => 
    line.trim().length > 5 && 
    !line.toLowerCase().includes('certification')
  ).slice(0, 3);
}

function calculateOptimizationScore(text: string): number {
  let score = 0;

  // Check for action verbs
  const actionVerbs = ['achieved', 'developed', 'implemented', 'managed', 'led', 'created', 'improved'];
  const actionVerbCount = actionVerbs.filter(verb => 
    text.toLowerCase().includes(verb)
  ).length;
  score += Math.min(actionVerbCount * 5, 25);

  // Check for quantifiable achievements
  const numberRegex = /\d+%|\$\d+|\d+\+/g;
  const numbers = text.match(numberRegex) || [];
  score += Math.min(numbers.length * 3, 20);

  // Check for relevant keywords
  const keywords = ['experience', 'skills', 'project', 'team', 'leadership'];
  const keywordCount = keywords.filter(keyword => 
    text.toLowerCase().includes(keyword)
  ).length;
  score += Math.min(keywordCount * 3, 15);

  // Length check
  if (text.length > 500 && text.length < 3000) {
    score += 20;
  } else if (text.length >= 3000) {
    score += 10;
  }

  // Structure check
  if (text.includes('experience') || text.includes('education')) {
    score += 20;
  }

  return Math.min(score, 100);
}

function calculateATSCompatibility(text: string): number {
  let score = 50; // Base score

  // Check for standard sections
  const sections = ['experience', 'education', 'skills'];
  const foundSections = sections.filter(section => 
    text.toLowerCase().includes(section)
  );
  score += foundSections.length * 10;

  // Check for contact information
  if (text.includes('@')) score += 10; // Email
  if (/\d{3}[-.]?\d{3}[-.]?\d{4}/.test(text)) score += 10; // Phone

  // Avoid complex formatting indicators
  if (text.includes('|') || text.includes('•')) {
    score -= 5; // Bullets might not parse well
  }

  return Math.min(Math.max(score, 0), 100);
}

function extractKeywords(text: string): string[] {
  // Extract important keywords
  const techKeywords = [
    'javascript', 'python', 'java', 'react', 'node', 'sql', 'aws', 'docker',
    'management', 'leadership', 'project', 'team', 'agile', 'scrum'
  ];

  return techKeywords.filter(keyword => 
    text.toLowerCase().includes(keyword.toLowerCase())
  );
}

function extractSkills(text: string): string[] {
  return extractSkillsList(text);
}

function extractExperience(text: string) {
  return extractWorkExperience(text);
}

function extractEducation(text: string) {
  return extractEducationDetails(text);
}

function generateSuggestions(text: string): string[] {
  const suggestions = [];

  if (!text.toLowerCase().includes('experience')) {
    suggestions.push('Add a work experience section');
  }

  if (!text.toLowerCase().includes('education')) {
    suggestions.push('Include your education background');
  }

  if (!text.toLowerCase().includes('skills')) {
    suggestions.push('Add a skills section');
  }

  const actionVerbs = ['achieved', 'developed', 'implemented', 'managed', 'led'];
  const hasActionVerbs = actionVerbs.some(verb => text.toLowerCase().includes(verb));
  if (!hasActionVerbs) {
    suggestions.push('Use more action verbs to describe your achievements');
  }

  const hasNumbers = /\d+%|\$\d+|\d+\+/.test(text);
  if (!hasNumbers) {
    suggestions.push('Include quantifiable achievements with numbers and percentages');
  }

  return suggestions;
}
