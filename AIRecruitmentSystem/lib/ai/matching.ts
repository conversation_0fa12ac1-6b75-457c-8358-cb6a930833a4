// AI Job Matching Logic
export async function calculateJobMatch(userProfile: any, job: any) {
  // This is a simplified AI matching algorithm
  // In a real implementation, you would use OpenAI API or other AI services
  
  let score = 0;
  const reasons = [];
  const recommendations = [];

  // Skills matching (40% weight)
  const userSkills = userProfile.skills || [];
  const jobSkills = job.skillsRequired || [];
  
  const matchingSkills = userSkills.filter((skill: string) =>
    jobSkills.some((jobSkill: string) =>
      skill.toLowerCase().includes(jobSkill.toLowerCase()) ||
      jobSkill.toLowerCase().includes(skill.toLowerCase())
    )
  );

  const skillsScore = jobSkills.length > 0 ? (matchingSkills.length / jobSkills.length) * 40 : 0;
  score += skillsScore;

  if (matchingSkills.length > 0) {
    reasons.push(`Strong skills match: ${matchingSkills.join(', ')}`);
  }

  // Experience level matching (25% weight)
  const experienceMatch = userProfile.experienceLevel === job.experienceLevel;
  if (experienceMatch) {
    score += 25;
    reasons.push(`Experience level matches: ${job.experienceLevel}`);
  } else if (userProfile.experienceLevel && job.experienceLevel) {
    const experienceLevels = ['ENTRY', 'JUNIOR', 'MID', 'SENIOR', 'EXECUTIVE'];
    const userIndex = experienceLevels.indexOf(userProfile.experienceLevel);
    const jobIndex = experienceLevels.indexOf(job.experienceLevel);
    const diff = Math.abs(userIndex - jobIndex);
    
    if (diff === 1) {
      score += 15;
      reasons.push('Experience level is close match');
    } else if (diff === 2) {
      score += 10;
      reasons.push('Experience level is reasonable match');
    }
  }

  // Location/Remote preference matching (20% weight)
  if (job.isRemote || userProfile.remotePreference === 'REMOTE') {
    score += 20;
    reasons.push('Remote work preference matches');
  } else if (userProfile.targetLocations?.includes(job.location)) {
    score += 20;
    reasons.push(`Location matches: ${job.location}`);
  } else if (userProfile.location === job.location) {
    score += 15;
    reasons.push(`Same location: ${job.location}`);
  }

  // Salary matching (15% weight)
  if (userProfile.targetSalaryMin && userProfile.targetSalaryMax && job.salaryMin && job.salaryMax) {
    const userMin = userProfile.targetSalaryMin;
    const userMax = userProfile.targetSalaryMax;
    const jobMin = job.salaryMin;
    const jobMax = job.salaryMax;

    // Check for overlap
    if (jobMax >= userMin && jobMin <= userMax) {
      score += 15;
      reasons.push('Salary range matches expectations');
    } else if (jobMax >= userMin * 0.9) {
      score += 10;
      reasons.push('Salary range is close to expectations');
    }
  }

  // Generate recommendations
  const missingSkills = jobSkills.filter((jobSkill: string) =>
    !userSkills.some((skill: string) =>
      skill.toLowerCase().includes(jobSkill.toLowerCase()) ||
      jobSkill.toLowerCase().includes(skill.toLowerCase())
    )
  );

  if (missingSkills.length > 0) {
    recommendations.push({
      type: 'skills',
      title: 'Develop Missing Skills',
      description: `Consider learning: ${missingSkills.slice(0, 3).join(', ')}`,
      priority: 'high'
    });
  }

  if (!experienceMatch && userProfile.experienceLevel && job.experienceLevel) {
    const experienceLevels = ['ENTRY', 'JUNIOR', 'MID', 'SENIOR', 'EXECUTIVE'];
    const userIndex = experienceLevels.indexOf(userProfile.experienceLevel);
    const jobIndex = experienceLevels.indexOf(job.experienceLevel);
    
    if (jobIndex > userIndex) {
      recommendations.push({
        type: 'experience',
        title: 'Gain More Experience',
        description: `This role requires ${job.experienceLevel} level experience`,
        priority: 'medium'
      });
    }
  }

  return {
    score: Math.min(100, Math.max(0, score)),
    reasons,
    recommendations
  };
}
