'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Star,
  TrendingUp,
  Brain,
  Award,
  MessageSquare,
  Calendar,
  ExternalLink,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { useState } from 'react';

interface CandidateRankingCardProps {
  candidate: {
    id: string;
    user: {
      id: string;
      name: string;
      email: string;
      image?: string;
      profile: {
        firstName?: string;
        lastName?: string;
        location?: string;
        experienceLevel?: string;
        skills: string[];
      };
    };
    overallScore: number;
    skillsMatchScore: number;
    experienceScore: number;
    roleCompatibilityScore: number;
    seniorityScore: number;
    technicalSkillsScore: number;
    softSkillsScore: number;
    ranking: number;
    isShortlisted: boolean;
    skillGaps?: any;
    recommendations?: any;
    reasonsForRanking?: any;
  };
  onShortlist?: (candidateId: string) => void;
  onMessage?: (candidateId: string) => void;
  onScheduleInterview?: (candidateId: string) => void;
  onViewProfile?: (candidateId: string) => void;
}

export default function CandidateRankingCard({
  candidate,
  onShortlist,
  onMessage,
  onScheduleInterview,
  onViewProfile
}: CandidateRankingCardProps) {
  const [showDetails, setShowDetails] = useState(false);

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBadgeVariant = (score: number) => {
    if (score >= 80) return 'default';
    if (score >= 60) return 'secondary';
    return 'destructive';
  };

  const getRankingBadge = (ranking: number) => {
    if (ranking === 1) return { icon: '🥇', text: '#1 Match', variant: 'default' as const };
    if (ranking === 2) return { icon: '🥈', text: '#2 Match', variant: 'secondary' as const };
    if (ranking === 3) return { icon: '🥉', text: '#3 Match', variant: 'secondary' as const };
    return { icon: '📊', text: `#${ranking} Match`, variant: 'outline' as const };
  };

  const rankingBadge = getRankingBadge(candidate.ranking);

  return (
    <Card className="hover:shadow-lg transition-shadow duration-200">
      <CardHeader className="pb-4">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <Avatar className="h-12 w-12">
              <AvatarImage src={candidate.user.image} />
              <AvatarFallback>
                {candidate.user.profile.firstName?.[0]}{candidate.user.profile.lastName?.[0]}
              </AvatarFallback>
            </Avatar>
            <div>
              <CardTitle className="text-lg">
                {candidate.user.profile.firstName} {candidate.user.profile.lastName}
              </CardTitle>
              <CardDescription className="flex items-center space-x-2">
                <span>{candidate.user.profile.location}</span>
                {candidate.user.profile.experienceLevel && (
                  <>
                    <span>•</span>
                    <span>{candidate.user.profile.experienceLevel}</span>
                  </>
                )}
              </CardDescription>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant={rankingBadge.variant}>
              {rankingBadge.icon} {rankingBadge.text}
            </Badge>
            {candidate.isShortlisted && (
              <Badge variant="default">
                <Star className="h-3 w-3 mr-1" />
                Shortlisted
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Overall Score */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Brain className="h-4 w-4 text-blue-600" />
            <span className="font-medium">AI Match Score</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className={`text-2xl font-bold ${getScoreColor(candidate.overallScore)}`}>
              {Math.round(candidate.overallScore)}%
            </span>
            <Badge variant={getScoreBadgeVariant(candidate.overallScore)}>
              {candidate.overallScore >= 80 ? 'Excellent' :
               candidate.overallScore >= 60 ? 'Good' : 'Fair'}
            </Badge>
          </div>
        </div>

        {/* Score Breakdown */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <div className="flex justify-between text-sm mb-1">
              <span>Skills Match</span>
              <span className="font-medium">{Math.round(candidate.skillsMatchScore)}%</span>
            </div>
            <Progress value={candidate.skillsMatchScore} className="h-2" />
          </div>
          <div>
            <div className="flex justify-between text-sm mb-1">
              <span>Experience</span>
              <span className="font-medium">{Math.round(candidate.experienceScore)}%</span>
            </div>
            <Progress value={candidate.experienceScore} className="h-2" />
          </div>
          <div>
            <div className="flex justify-between text-sm mb-1">
              <span>Role Fit</span>
              <span className="font-medium">{Math.round(candidate.roleCompatibilityScore)}%</span>
            </div>
            <Progress value={candidate.roleCompatibilityScore} className="h-2" />
          </div>
          <div>
            <div className="flex justify-between text-sm mb-1">
              <span>Technical Skills</span>
              <span className="font-medium">{Math.round(candidate.technicalSkillsScore)}%</span>
            </div>
            <Progress value={candidate.technicalSkillsScore} className="h-2" />
          </div>
        </div>

        {/* Skills Preview */}
        <div>
          <h4 className="text-sm font-medium mb-2">Key Skills</h4>
          <div className="flex flex-wrap gap-1">
            {candidate.user.profile.skills.slice(0, 4).map((skill, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                {skill}
              </Badge>
            ))}
            {candidate.user.profile.skills.length > 4 && (
              <Badge variant="outline" className="text-xs">
                +{candidate.user.profile.skills.length - 4} more
              </Badge>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex space-x-2 pt-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() => onViewProfile?.(candidate.user.id)}
          >
            <ExternalLink className="h-3 w-3 mr-1" />
            View Profile
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => onMessage?.(candidate.user.id)}
          >
            <MessageSquare className="h-3 w-3 mr-1" />
            Message
          </Button>
          <Button
            size="sm"
            onClick={() => onScheduleInterview?.(candidate.user.id)}
          >
            <Calendar className="h-3 w-3 mr-1" />
            Interview
          </Button>
          {!candidate.isShortlisted && (
            <Button
              size="sm"
              variant="secondary"
              onClick={() => onShortlist?.(candidate.user.id)}
            >
              <Star className="h-3 w-3 mr-1" />
              Shortlist
            </Button>
          )}
        </div>

        {/* Expandable Details */}
        <div className="border-t pt-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowDetails(!showDetails)}
            className="w-full"
          >
            {showDetails ? (
              <>
                <ChevronUp className="h-4 w-4 mr-2" />
                Hide Details
              </>
            ) : (
              <>
                <ChevronDown className="h-4 w-4 mr-2" />
                Show Details
              </>
            )}
          </Button>

          {showDetails && (
            <div className="mt-4 space-y-4">
              {/* Ranking Reasons */}
              {candidate.reasonsForRanking && (
                <div>
                  <h5 className="text-sm font-medium mb-2">Why This Ranking?</h5>
                  <ul className="text-sm text-gray-600 space-y-1">
                    {candidate.reasonsForRanking.map((reason: string, index: number) => (
                      <li key={index} className="flex items-start">
                        <span className="text-blue-500 mr-2">•</span>
                        {reason}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Skill Gaps */}
              {candidate.skillGaps && candidate.skillGaps.missingSkills?.length > 0 && (
                <div>
                  <h5 className="text-sm font-medium mb-2">Skill Gaps</h5>
                  <div className="flex flex-wrap gap-1">
                    {candidate.skillGaps.missingSkills.slice(0, 5).map((skill: string, index: number) => (
                      <Badge key={index} variant="destructive" className="text-xs">
                        {skill}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Recommendations */}
              {candidate.recommendations && candidate.recommendations.length > 0 && (
                <div>
                  <h5 className="text-sm font-medium mb-2">Recommendations</h5>
                  <div className="space-y-2">
                    {candidate.recommendations.map((rec: any, index: number) => (
                      <div key={index} className="text-sm p-2 bg-gray-50 rounded">
                        <div className="font-medium">{rec.title}</div>
                        <div className="text-gray-600">{rec.description}</div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
