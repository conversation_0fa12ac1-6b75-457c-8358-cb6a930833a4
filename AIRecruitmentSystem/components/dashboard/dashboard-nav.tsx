'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  LayoutDashboard,
  Briefcase,
  Users,
  MessageSquare,
  FileText,
  Calendar,
  Settings,
  BarChart3,
  Shield,
  Brain,
  UserCheck,
  Building
} from 'lucide-react';

interface DashboardNavProps {
  userType: 'JOB_SEEKER' | 'RECRUITER' | 'ADMIN';
}

export default function DashboardNav({ userType }: DashboardNavProps) {
  const pathname = usePathname();

  const jobSeekerNavItems = [
    {
      title: 'Dashboard',
      href: '/dashboard',
      icon: LayoutDashboard,
    },
    {
      title: 'Job Matches',
      href: '/dashboard/jobs',
      icon: Briefcase,
      badge: 'AI'
    },
    {
      title: 'Applications',
      href: '/dashboard/applications',
      icon: FileText,
    },
    {
      title: 'Resume Builder',
      href: '/dashboard/resume',
      icon: FileText,
      badge: 'New'
    },
    {
      title: 'Interviews',
      href: '/dashboard/interviews',
      icon: Calendar,
    },
    {
      title: 'Messages',
      href: '/dashboard/messages',
      icon: MessageSquare,
    },
    {
      title: 'Settings',
      href: '/dashboard/settings',
      icon: Settings,
    },
  ];

  const recruiterNavItems = [
    {
      title: 'Dashboard',
      href: '/dashboard',
      icon: LayoutDashboard,
    },
    {
      title: 'Jobs',
      href: '/dashboard/jobs',
      icon: Briefcase,
    },
    {
      title: 'Candidates',
      href: '/dashboard/candidates',
      icon: Users,
      badge: 'AI'
    },
    {
      title: 'Applications',
      href: '/dashboard/applications',
      icon: FileText,
    },
    {
      title: 'Interviews',
      href: '/dashboard/interviews',
      icon: Calendar,
    },
    {
      title: 'Messages',
      href: '/dashboard/messages',
      icon: MessageSquare,
    },
    {
      title: 'Analytics',
      href: '/dashboard/analytics',
      icon: BarChart3,
    },
    {
      title: 'Settings',
      href: '/dashboard/settings',
      icon: Settings,
    },
  ];

  const adminNavItems = [
    {
      title: 'Dashboard',
      href: '/dashboard',
      icon: LayoutDashboard,
    },
    {
      title: 'Users',
      href: '/dashboard/admin/users',
      icon: Users,
    },
    {
      title: 'Companies',
      href: '/dashboard/admin/companies',
      icon: Building,
    },
    {
      title: 'Analytics',
      href: '/dashboard/admin/analytics',
      icon: BarChart3,
    },
    {
      title: 'AI Models',
      href: '/dashboard/admin/ai-models',
      icon: Brain,
    },
    {
      title: 'Compliance',
      href: '/dashboard/compliance',
      icon: Shield,
    },
    {
      title: 'Audit Logs',
      href: '/dashboard/admin/audit-logs',
      icon: FileText,
    },
    {
      title: 'System Health',
      href: '/dashboard/admin/system-health',
      icon: UserCheck,
    },
    {
      title: 'Settings',
      href: '/dashboard/settings',
      icon: Settings,
    },
  ];

  const getNavItems = () => {
    switch (userType) {
      case 'JOB_SEEKER':
        return jobSeekerNavItems;
      case 'RECRUITER':
        return recruiterNavItems;
      case 'ADMIN':
        return adminNavItems;
      default:
        return jobSeekerNavItems;
    }
  };

  const navItems = getNavItems();

  return (
    <nav className="space-y-2">
      {navItems.map((item) => {
        const isActive = pathname === item.href;
        const Icon = item.icon;

        return (
          <Link key={item.href} href={item.href}>
            <Button
              variant={isActive ? 'secondary' : 'ghost'}
              className={cn(
                'w-full justify-start',
                isActive && 'bg-secondary'
              )}
            >
              <Icon className="mr-2 h-4 w-4" />
              {item.title}
              {item.badge && (
                <Badge variant="secondary" className="ml-auto">
                  {item.badge}
                </Badge>
              )}
            </Button>
          </Link>
        );
      })}
    </nav>
  );
}
