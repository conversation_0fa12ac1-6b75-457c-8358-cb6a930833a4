# Database
DATABASE_URL="postgresql://username:password@localhost:5432/ai_recruitment_db"

# NextAuth.js
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here"

# OpenAI API
OPENAI_API_KEY="your-openai-api-key"

# Email Configuration
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# File Upload
UPLOAD_DIR="./uploads"
MAX_FILE_SIZE="10485760"

# Redis (for caching and sessions)
REDIS_URL="redis://localhost:6379"

# Job Scraping APIs
INDEED_API_KEY="your-indeed-api-key"
LINKEDIN_API_KEY="your-linkedin-api-key"
GLASSDOOR_API_KEY="your-glassdoor-api-key"

# Stripe (for payments)
STRIPE_PUBLISHABLE_KEY="pk_test_..."
STRIPE_SECRET_KEY="sk_test_..."
STRIPE_WEBHOOK_SECRET="whsec_..."

# Analytics
GOOGLE_ANALYTICS_ID="GA_MEASUREMENT_ID"

# Security
JWT_SECRET="your-jwt-secret"
ENCRYPTION_KEY="your-encryption-key"

# Feature Flags
ENABLE_AI_MATCHING="true"
ENABLE_JOB_SCRAPING="true"
ENABLE_REAL_TIME_MESSAGING="true"
ENABLE_VIDEO_INTERVIEWS="true"
